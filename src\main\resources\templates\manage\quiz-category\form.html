<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title th:text="${isEdit ? '문제 카테고리 수정' : '문제 카테고리 등록'} + ' - 한림공원 관리시스템'">문제 카테고리 등록 - 한림공원 관리시스템</title>
    <meta name="description" th:content="${isEdit ? '문제 카테고리 정보를 수정합니다.' : '새로운 문제 카테고리를 등록합니다.'}">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/content/quiz-category.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="page-header bg-light border-bottom py-4">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-1 text-dark">
                            <i class="fas fa-tags me-2 text-primary"></i>
                            <span th:text="${isEdit ? '카테고리 수정' : '카테고리 등록'}">카테고리 등록</span>
                        </h2>
                        <p class="mb-0 text-muted" th:text="${isEdit ? '카테고리 정보를 수정합니다.' : '새로운 카테고리를 등록합니다.'}">새로운 카테고리를 등록합니다.</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a th:href="@{/manage/quiz-category}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>목록으로 돌아가기
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section class="py-4">
            <div class="container-fluid">
                <div class="form-container">
                    <div class="card form-card">
                        <div class="form-header">
                            <h3 class="mb-0">
                                <i th:class="${isEdit ? 'fas fa-edit' : 'fas fa-plus'} me-2"></i>
                                <span th:text="${isEdit ? '카테고리 정보 수정' : '새 카테고리 등록'}">새 카테고리 등록</span>
                            </h3>
                        </div>
                        
                        <div class="form-body">
                            <form id="categoryForm" th:action="${isEdit ? '/manage/quiz-category/api/' + category.categoryId : '/manage/quiz-category/api/create'}" 
                                  th:method="${isEdit ? 'PUT' : 'POST'}">
                                
                                <!-- 카테고리명 선택 -->
                                <div class="mb-4">
                                    <label for="categoryName" class="form-label">
                                        카테고리명 <span class="required">*</span>
                                    </label>
                                    <select class="form-select" id="categoryName" name="categoryName" data-category-select required>
                                        <option value="">카테고리를 선택하세요</option>
                                        <option th:each="categoryName : ${categoryNames}"
                                                th:value="${categoryName.name()}"
                                                th:text="${categoryName.displayName}"
                                                th:selected="${isEdit and category.categoryName == categoryName}">
                                            카테고리명
                                        </option>
                                    </select>
                                    <div class="invalid-feedback">
                                        카테고리명을 선택해주세요.
                                    </div>
                                </div>

                                <!-- 카테고리 설명 -->
                                <div class="mb-4">
                                    <label for="description" class="form-label">카테고리 설명</label>
                                    <textarea class="form-control" id="description" name="description" rows="4"
                                              data-description-input
                                              placeholder="카테고리에 대한 설명을 입력하세요 (선택사항)"
                                              th:text="${isEdit ? category.description : ''}"></textarea>
                                    <div class="form-text">최대 1000자까지 입력 가능합니다.</div>
                                    <div class="invalid-feedback">
                                        설명은 1000자를 초과할 수 없습니다.
                                    </div>
                                </div>

                                <!-- 미리보기 -->
                                <div class="category-preview" id="categoryPreview" data-preview-container style="display: none;">
                                    <h6 class="mb-2"><i class="fas fa-eye me-2"></i>미리보기</h6>
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <div class="fw-bold text-primary" id="previewName" data-preview-name>카테고리명</div>
                                            <div class="text-muted small" id="previewDescription" data-preview-description>카테고리 설명</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 버튼 그룹 -->
                                <div class="d-flex justify-content-end gap-3 mt-4">
                                    <a th:href="@{/manage/quiz-category}" class="btn btn-outline-secondary btn-cancel" data-cancel-btn>
                                        <i class="fas fa-times me-2"></i>취소
                                    </a>
                                    <button type="submit" class="btn btn-primary btn-submit" id="submitBtn" data-submit-btn>
                                        <i th:class="${isEdit ? 'fas fa-save' : 'fas fa-plus'} me-2"></i>
                                        <span th:text="${isEdit ? '수정하기' : '등록하기'}">등록하기</span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script th:src="@{/js/manage/quiz-category.js}"></script>
        <script th:inline="javascript">
            // 페이지 로드 시 초기화
            document.addEventListener('DOMContentLoaded', function() {
                const isEdit = /*[[${isEdit}]]*/ false;
                const categoryData = isEdit ? /*[[${category}]]*/ null : null;
                
                QuizCategoryManager.initForm(isEdit, categoryData);
            });
        </script>
    </th:block>
</body>
</html>
