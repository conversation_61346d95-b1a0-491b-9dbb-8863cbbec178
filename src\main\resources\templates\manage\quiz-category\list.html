<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>문제 카테고리 관리 - 한림공원 관리시스템</title>
    <meta name="description" content="문제 카테고리를 관리합니다.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/content/quiz-category.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="page-header bg-light border-bottom py-4">
            <div class="container-fluid">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h2 class="mb-1 text-dark">
                            <i class="fas fa-tags me-2 text-primary"></i>콘텐츠 카테고리 관리
                        </h2>
                        <p class="mb-0 text-muted">문제의 카테고리를 관리합니다.</p>
                    </div>
                    <div class="col-md-4 text-md-end">
                        <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>새 카테고리 등록
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section class="py-4">
            <div class="container-fluid">
                <!-- 에러 메시지 -->
                <div th:if="${errorMessage}" class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span th:text="${errorMessage}">에러 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 카테고리 목록 -->
                <div class="row">
                    <!-- 카테고리가 있는 경우 -->
                    <div th:if="${categories != null and !categories.isEmpty()}" class="col-12">
                        <div class="row" id="categoryList">
                            <div th:each="category : ${categories}" class="col-lg-4 col-md-6 mb-4">
                                <div class="card category-card h-100" data-category-card th:data-category-id="${category.categoryId}">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h5 class="category-name mb-0" data-category-name th:text="${category.categoryName.displayName}">카테고리명</h5>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button"
                                                        data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li>
                                                        <a class="dropdown-item" th:href="@{/manage/quiz-category/{id}/edit(id=${category.categoryId})}">
                                                            <i class="fas fa-edit me-2"></i>수정
                                                        </a>
                                                    </li>
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <button class="dropdown-item text-danger"
                                                                th:data-delete-btn="${category.categoryId}"
                                                                th:onclick="|deleteCategory(${category.categoryId}, '${category.categoryName.displayName}')|">
                                                            <i class="fas fa-trash me-2"></i>삭제
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        
                                        <p class="category-description mb-3" data-category-description
                                           th:text="${category.description != null and !category.description.isEmpty() ? category.description : '설명이 없습니다.'}">
                                            카테고리 설명
                                        </p>

                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                ID: <span th:text="${category.categoryId}" data-category-id-display>1</span>
                                            </small>
                                            <div>
                                                <a th:href="@{/manage/quiz-category/{id}/edit(id=${category.categoryId})}"
                                                   class="btn btn-sm btn-outline-primary btn-action" data-edit-btn>
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger btn-action"
                                                        th:data-delete-btn-small="${category.categoryId}"
                                                        th:onclick="|deleteCategory(${category.categoryId}, '${category.categoryName.displayName}')|">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 카테고리가 없는 경우 -->
                    <div th:if="${categories == null or categories.isEmpty()}" class="col-12">
                        <div class="empty-state">
                            <i class="fas fa-question-circle"></i>
                            <h3>등록된 문제 카테고리가 없습니다</h3>
                            <p class="mb-4">새로운 문제 카테고리를 등록해보세요.</p>
                            <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary btn-lg">
                                <i class="fas fa-plus me-2"></i>첫 번째 카테고리 등록하기
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script th:src="@{/js/manage/quiz-category.js}"></script>
        <script>
            // 페이지 로드 시 초기화
            document.addEventListener('DOMContentLoaded', function() {
                QuizCategoryManager.init();
            });
        </script>
    </th:block>
</body>
</html>
